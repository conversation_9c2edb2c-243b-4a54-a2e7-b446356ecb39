import Modal from 'react-bootstrap/Modal'

import styled from 'styled-components'
import { theme } from 'styles/theme'

import { ReactComponent as CloseIcon } from 'img/icons/closeIcon.svg'

export const ModalStyled = styled(Modal)`
  .modal-content {
    width: 70vw;
    min-width: 30rem;
  }
`

export const HeaderStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;

  padding: 0.8rem 1.5rem;

  border-bottom: 1px solid rgba(10, 12, 17, 0.1);
`

export const TitleStyled = styled.div`
  display: flex;
  align-items: center;

  gap: 0.6rem;

  color: #0a0c11;
  font-size: 1rem;
  font-family: ${theme.fonts.normal};
`

export const SubtitleStyled = styled.p`
  color: ${theme.colorsNew.darkGrey500};
  font-size: 0.9rem;
  font-family: ${theme.fonts.light};
`

export const CloseButtonStyled = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;

  padding: 0.6rem;

  border: none;
  border-radius: 50%;
  background: rgba(28, 34, 43, 0.04);
  color: ${theme.colorsNew.darkGrey500};

  :hover,
  :focus {
    background: rgba(28, 34, 43, 0.1);
  }
`

export const CloseIconStyled = styled(CloseIcon)`
  width: 0.8rem;
  height: 0.8rem;
  stroke: currentColor;
`

export const BodyStyled = styled.div`
  display: flex;
`

export const ListStyled = styled.div`
  display: flex;
  flex-direction: column;

  gap: 0.2rem;
  flex: 1;
  padding: 0.8rem 1rem;

  border-right: 1px solid rgba(10, 12, 17, 0.1);
`

export const ListItemStyled = styled.button<{ $isActive?: boolean }>`
  display: flex;
  align-items: center;
  justify-content: space-between;

  padding: 0.5rem 0.8rem;

  border: 0;
  border-radius: 0.6rem;
  background-color: ${({ $isActive }) => ($isActive ? '#EDF2F7' : '#fff')};

  color: ${({ $isActive }) =>
    $isActive ? theme.colorsNew.darkGrey500 : 'rgba(69, 84, 104, 0.6)'};
  font-size: 0.9rem;
  font-family: ${theme.fonts.normal};

  span {
    display: flex;
    align-items: center;
    justify-content: center;

    width: 1rem;
    height: 1rem;

    border-radius: 50%;
    background-color: #667085;

    color: #fff;
    font-size: 0.65rem;
    font-family: ${theme.fonts.bold};
    line-height: normal;
  }

  :hover,
  :focus {
    color: ${theme.colorsNew.darkGrey500};
    background-color: #edf2f7;
  }
`

export const BlockStyled = styled.div`
  display: flex;
  flex-direction: column;

  flex: 4.5;

  background-color: #edf2f7;
  border-bottom-right-radius: 0.8rem;
`

export const PeriodWrapStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;

  padding: 1.5rem;
`

export const PeriodTabsStyled = styled.div`
  display: flex;
  align-items: center;

  gap: 0.6rem;
  width: 100%;
`

export const PeriodItemStyled = styled.button<{ $isActive?: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;

  gap: 0.6rem;
  flex: 1;
  padding: 0.4rem 1rem;

  border: 1px solid
    ${({ $isActive }) =>
      $isActive ? 'rgba(69, 84, 104, 0.05)' : 'transparent'};
  border-radius: 0.6rem;
  background-color: ${({ $isActive }) => ($isActive ? '#32ADE6' : '#fff')};

  color: ${({ $isActive }) =>
    $isActive ? '#fff' : theme.colorsNew.darkGrey500};
  font-size: 0.9rem;
  font-family: ${theme.fonts.normal};
  line-height: normal;

  span {
    display: flex;
    align-items: center;
    justify-content: center;

    width: 0.9rem;
    height: 0.9rem;

    border-radius: 50%;
    background-color: ${({ $isActive }) => ($isActive ? '#fff' : '#667085')};

    color: ${({ $isActive }) => ($isActive ? '#32ADE6' : '#fff')};
    font-size: 0.65rem;
    font-family: ${theme.fonts.normal};
    line-height: normal;
  }

  :hover,
  :focus {
    background-color: ${({ $isActive }) =>
      $isActive ? '#32ade6' : 'rgba(69, 84, 104, 0.1)'};
  }
`

export const ScrollContainerStyled = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;

  gap: 1.5rem;
  min-height: 25rem;
  max-height: 60vh;
  padding: 0 1.5rem 1.5rem;

  overflow-y: auto;
`

export const TypeSectionStyled = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;

  width: 100%;
`

export const CardEmptyStyled = styled.div`
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;

  gap: 0;
  flex: 1;
  width: 100%;
  padding: 1.5rem;

  border-radius: 0.8rem;
  background-color: rgba(255, 255, 255, 0.6);

  color: #32ade6;
  font-size: 1.1rem;
  font-family: ${theme.fonts.normal};

  img {
    width: 1.5rem;
    height: 1.5rem;
  }
`
