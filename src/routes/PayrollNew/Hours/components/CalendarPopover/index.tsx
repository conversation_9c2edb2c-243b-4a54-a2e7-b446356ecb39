import React, { forwardRef, useState, useEffect } from 'react'
import { enUS } from 'react-day-picker/locale'
import { useSelector } from 'react-redux'
import { I18n } from 'react-redux-i18n'

import dayjs, { Dayjs } from 'dayjs'
import moment from 'moment'
import { RootState } from 'store/reducers'

import { OptionType } from 'components/ui/Select'

import { AttendanceSettings } from 'types/attendance'
import { ALL_DAYS } from 'utils/constants'

import {
  PopoverStyled,
  ContainerStyled,
  CloseButtonStyled,
  CloseIconStyled,
  MainStyled,
  CalendarBlockStyled,
  SelectBlockStyled,
  CustomSelectStyled,
  DayPickerStyled,
  ListBlockStyled,
  ListLabelStyled,
  ListStyled,
  ListWrapStyled,
  CircleStyled,
  CheckIconStyled,
  ListItemStyled,
  FooterStyled,
  ResetButtonStyled,
  OpenButtonStyled
} from '../../../styles/CalendarPopover.styles'

// Helper function to calculate effective period - moved outside component for performance
const calculateEffectivePeriod = (
  startOfPeriod: dayjs.Dayjs,
  currentPeriodOffset: number,
  payrollLength: number,
  activeWeekPeriodTab: string
) => {
  // When using week tabs, we need to ensure we're working with biweekly periods
  // If payrollLength is 7 but we're in week1/week2 tab, we need to treat it as biweekly (14 days)
  const isWeeklyMode = payrollLength === 7
  const effectivePayrollLength = (activeWeekPeriodTab === 'week1' || activeWeekPeriodTab === 'week2') && isWeeklyMode ? 14 : payrollLength
  
  // Calculate the base biweekly period start using the ORIGINAL startOfPeriod
  // No adjustments needed here since we're using the stable reference
  let basePeriodStart = startOfPeriod
    .clone()
    .add(currentPeriodOffset * effectivePayrollLength, 'days')
  
  if (activeWeekPeriodTab === 'week1') {
    // Week 1: First 7 days of the bi-weekly period
    return {
      start: basePeriodStart,
      length: 7
    }
  } else if (activeWeekPeriodTab === 'week2') {
    // Week 2: Second 7 days of the bi-weekly period
    return {
      start: basePeriodStart.clone().add(7, 'days'),
      length: 7
    }
  } else {
    // Biweekly: Full period
    return {
      start: basePeriodStart,
      length: payrollLength
    }
  }
}

type Props = {
  onClose: () => void
  startOfPeriod: Dayjs
  payrollLength: number
  currentPeriodOffset: number
  onPeriodSelect: (offset: number) => void
  attendanceSettings: AttendanceSettings
  payrollStartingDay?: string
  activeWeekPeriodTab?: string
}

const CalendarPopover = forwardRef<HTMLDivElement, Props>(
  (
    {
      onClose,
      startOfPeriod,
      payrollLength,
      currentPeriodOffset,
      onPeriodSelect,
      attendanceSettings,
      payrollStartingDay = 'Monday',
      activeWeekPeriodTab = 'biweekly',
      ...props
    },
    ref,
    ...rest
  ) => {
    // Store the original startOfPeriod when component mounts - this will be our stable reference
    // Use useRef to ensure it's truly stable and never changes
    const originalStartOfPeriodRef = React.useRef<dayjs.Dayjs | null>(null)
    if (originalStartOfPeriodRef.current === null) {
      originalStartOfPeriodRef.current = startOfPeriod.clone()
    }
    const originalStartOfPeriod = originalStartOfPeriodRef.current
    
    // Local state to track the selected period within the modal
    // This doesn't update the context until "Open selected" is clicked
    const [localSelectedOffset, setLocalSelectedOffset] = React.useState(currentPeriodOffset)

    // Reset local selection to match context when modal opens/context changes
    React.useEffect(() => {
      setLocalSelectedOffset(currentPeriodOffset)
    }, [currentPeriodOffset])
    
    // Calculate effective period based on active week tab
    const getEffectivePeriod = React.useCallback(() => {
      return calculateEffectivePeriod(originalStartOfPeriod, currentPeriodOffset, payrollLength, activeWeekPeriodTab)
    }, [originalStartOfPeriod, currentPeriodOffset, payrollLength, activeWeekPeriodTab])

    const effectivePeriod = getEffectivePeriod()
    const currentPeriodStart = effectivePeriod.start
    const effectivePayrollLength = effectivePeriod.length

    // Initialize month/year dropdowns to match the current selected period
    const [selectedYear, setSelectedYear] = useState(currentPeriodStart.format('YYYY'))
    const [selectedMonth, setSelectedMonth] = useState(currentPeriodStart.format('M'))

    const currentPeriodEnd = currentPeriodStart
      .clone()
      .add(effectivePayrollLength - 1, 'days')

    const [selectedRange, setSelectedRange] = useState<{
      from: Date | undefined
      to?: Date | undefined
    }>({
      from: currentPeriodStart.toDate(),
      to: currentPeriodEnd.toDate()
    })

    const isLocaleFr =
      useSelector((state: RootState) => state.i18n.locale) === 'fr'

    // Calculate week starting day for calendar (0 = Sunday, 1 = Monday, etc.)
    const weekStartsOn = React.useMemo(() => {
      const dayIndex = ALL_DAYS[payrollStartingDay] || 1 // Default to Monday
      // ALL_DAYS already uses 0-6 where Sunday=0, Monday=1, etc. - perfect for DayPicker
      return dayIndex as 0 | 1 | 2 | 3 | 4 | 5 | 6
    }, [payrollStartingDay])

    // Update selected range when currentPeriodOffset changes from parent
    useEffect(() => {
      let newPeriodStart, newPeriodEnd

      if (activeWeekPeriodTab === 'week1') {
        // Week 1: First 7 days of the biweekly period
        const baseBiweeklyStart = startOfPeriod
          .clone()
          .add(currentPeriodOffset * 14, 'days')
        newPeriodStart = baseBiweeklyStart
        newPeriodEnd = baseBiweeklyStart.clone().add(6, 'days')
      } else if (activeWeekPeriodTab === 'week2') {
        // Week 2: Second 7 days of the biweekly period
        const baseBiweeklyStart = startOfPeriod
          .clone()
          .add(currentPeriodOffset * 14, 'days')
        newPeriodStart = baseBiweeklyStart.clone().add(7, 'days')
        newPeriodEnd = baseBiweeklyStart.clone().add(13, 'days')
      } else {
        // Biweekly: Full 14-day period
        const effectivePayrollLength = payrollLength === 7 ? 14 : payrollLength
        newPeriodStart = startOfPeriod
          .clone()
          .add(currentPeriodOffset * effectivePayrollLength, 'days')
        newPeriodEnd = newPeriodStart.clone().add(effectivePayrollLength - 1, 'days')
      }

      setSelectedRange({
        from: newPeriodStart.toDate(),
        to: newPeriodEnd.toDate()
      })
    }, [startOfPeriod, currentPeriodOffset, payrollLength, activeWeekPeriodTab])

    // Initialize month/year dropdowns only once when component mounts
    const [isInitialized, setIsInitialized] = React.useState(false)
    React.useEffect(() => {
      if (!isInitialized && selectedRange.from) {
        const rangeDate = dayjs(selectedRange.from)
        setSelectedYear(rangeDate.format('YYYY'))
        setSelectedMonth(rangeDate.format('M'))
        setIsInitialized(true)
      }
    }, [selectedRange.from, isInitialized]) // Only initialize once

    // Helper function to find the offset for a given date
    const getOffsetForDate = React.useCallback((date: dayjs.Dayjs, baseStartOfPeriod?: dayjs.Dayjs) => {
      const periodStart = baseStartOfPeriod || startOfPeriod
      // Calculate effective payroll length for biweekly calculations
      const isWeeklyMode = payrollLength === 7
      const effectivePayrollLength = (activeWeekPeriodTab === 'week1' || activeWeekPeriodTab === 'week2') && isWeeklyMode ? 14 : payrollLength

      // Calculate how many days difference between the target date and base period start
      const daysDiff = date.diff(periodStart, 'days')

      // Calculate which period this date should fall into
      // Each period is effectivePayrollLength days, so divide by effectivePayrollLength and floor
      const estimatedOffset = Math.floor(daysDiff / effectivePayrollLength)

      // Check a range around the estimated offset to account for period boundaries
      for (let offset = estimatedOffset - 2; offset <= estimatedOffset + 2; offset++) {
        const testPeriodStart = periodStart.clone().add(offset * effectivePayrollLength, 'days')
        const periodEnd = testPeriodStart.clone().add(effectivePayrollLength - 1, 'days')
        if (date.isBetween(testPeriodStart, periodEnd, 'day', '[]')) {
          return offset
        }
      }

      // Fallback: broader search if estimation failed
      for (let offset = -50; offset <= 50; offset++) {
        const testPeriodStart = periodStart.clone().add(offset * effectivePayrollLength, 'days')
        const periodEnd = testPeriodStart.clone().add(effectivePayrollLength - 1, 'days')
        if (date.isBetween(testPeriodStart, periodEnd, 'day', '[]')) {
          return offset
        }
      }

      return 0
    }, [startOfPeriod, payrollLength, activeWeekPeriodTab])

    // Generate period list: center around the locally selected period or current month/year
    const periodList = React.useMemo(() => {
      const periods = []
      // Calculate effective payroll length based on active tab
      let effectivePayrollLength = payrollLength
      if (activeWeekPeriodTab === 'biweekly') {
        effectivePayrollLength = 14 // Always use 14 days for biweekly
      } else if (activeWeekPeriodTab === 'week1' || activeWeekPeriodTab === 'week2') {
        effectivePayrollLength = payrollLength === 7 ? 14 : payrollLength // Use biweekly base for weekly tabs
      }

      // Use the ORIGINAL startOfPeriod for consistent calculations - no adjustments needed
      let normalizedStartOfPeriod = originalStartOfPeriod.clone()

      // Find a period that intersects with the currently displayed month/year
      const displayedMonth = parseInt(selectedMonth)
      const displayedYear = parseInt(selectedYear)
      const monthStart = dayjs(`${displayedYear}-${displayedMonth.toString().padStart(2, '0')}-01`)

      // Find offset for a period that intersects with this month
      let centerOffset = localSelectedOffset
      for (let testOffset = localSelectedOffset - 10; testOffset <= localSelectedOffset + 10; testOffset++) {
        const testPeriodStart = normalizedStartOfPeriod.clone().add(testOffset * effectivePayrollLength, 'days')
        const testPeriodEnd = testPeriodStart.clone().add(effectivePayrollLength - 1, 'days')

        // Check if this period intersects with the displayed month
        if ((testPeriodStart.month() === displayedMonth - 1 && testPeriodStart.year() === displayedYear) ||
            (testPeriodEnd.month() === displayedMonth - 1 && testPeriodEnd.year() === displayedYear) ||
            (testPeriodStart.isBefore(monthStart) && testPeriodEnd.isAfter(monthStart))) {
          centerOffset = testOffset
          break
        }
      }

      // Center the list around the found offset (5 before, current, 5 after)
      for (let offset = centerOffset - 5; offset <= centerOffset + 5; offset++) {
        // Calculate period start and end based on active week tab
        let periodStart, periodEnd

        if (activeWeekPeriodTab === 'week1') {
          // Week 1: First 7 days of each bi-weekly period
          // Use biweekly increments to find the base biweekly period
          let baseBiweeklyStart = normalizedStartOfPeriod
            .clone()
            .add(offset * 14, 'days') // Always use 14-day increments for biweekly base
          periodStart = baseBiweeklyStart
          periodEnd = baseBiweeklyStart.clone().add(6, 'days')
        } else if (activeWeekPeriodTab === 'week2') {
          // Week 2: Second 7 days of each bi-weekly period
          // Match the main component logic: basePeriodStart + 7 days
          let basePeriodStart = normalizedStartOfPeriod
            .clone()
            .add(offset * 14, 'days') // Use 14-day increments to find biweekly base
          periodStart = basePeriodStart.clone().add(7, 'days')
          periodEnd = basePeriodStart.clone().add(13, 'days')
        } else {
          // Biweekly: Full period
          let basePeriodStart = normalizedStartOfPeriod
            .clone()
            .add(offset * effectivePayrollLength, 'days')
          periodStart = basePeriodStart
          periodEnd = basePeriodStart.clone().add(effectivePayrollLength - 1, 'days')
        }

        // Use local selected offset instead of context offset for selection state
        const isSelected = offset === localSelectedOffset
        periods.push({
          offset,
          start: periodStart,
          end: periodEnd,
          label: `${periodStart.format(isLocaleFr ? 'DD' : 'MMM DD')} - ${periodEnd.format(isLocaleFr ? 'DD MMM' : 'MMM DD')}`,
          isCurrentPeriod: offset === 0,
          isSelected: isSelected
        })
      }

      return periods
    }, [originalStartOfPeriod, payrollLength, localSelectedOffset, isLocaleFr, activeWeekPeriodTab, selectedMonth, selectedYear])

    const years = Array.from({ length: 6 }, (_, i) => 2022 + i).map(c =>
      moment().year(c).format('YYYY')
    )

    const yearOptions = years.map(c => ({
      value: c,
      label: c
    }))

    const monthsOptions = Array.from({ length: 12 }, (_, i) => i).map(c => ({
      value: `${c + 1}`,
      label: moment().month(c).format('MMMM')
    }))

    // const handleDateSelect = (date: Date | undefined) => {
    //   // Ignore programmatic updates
    //   if (isUpdatingProgrammatically) {
    //     return
    //   }

    //   if (!date) {
    //     setSelectedRange({ from: undefined, to: undefined })
    //     return
    //   }

    //   // Find which payroll period this date falls into
    //   const selectedDate = dayjs(date)

    //   // Calculate the offset from the current period
    //   let bestOffset = 0
    //   let minDistance = Infinity

    //   // Check periods from -10 to +10 to find the closest match
    //   for (let offset = -10; offset <= 10; offset++) {
    //     const periodStart = startOfPeriod
    //       .clone()
    //       .add(offset * payrollLength, 'days')
    //     const periodEnd = periodStart.clone().add(payrollLength - 1, 'days')

    //     if (selectedDate.isBetween(periodStart, periodEnd, 'day', '[]')) {
    //       bestOffset = offset
    //       break
    //     }

    //     const distance = Math.min(
    //       Math.abs(selectedDate.diff(periodStart, 'days')),
    //       Math.abs(selectedDate.diff(periodEnd, 'days'))
    //     )
    //     if (distance < minDistance) {
    //       minDistance = distance
    //       bestOffset = offset
    //     }
    //   }

    //   // Set the selected range to the found period
    //   const periodStart = startOfPeriod
    //     .clone()
    //     .add(bestOffset * payrollLength, 'days')
    //   const periodEnd = periodStart.clone().add(payrollLength - 1, 'days')

    //   setSelectedRange({
    //     from: periodStart.toDate(),
    //     to: periodEnd.toDate()
    //   })

    //   // Update month/year dropdowns to match the selected period
    //   setIsUpdatingProgrammatically(true)
    //   setSelectedYear(periodStart.format('YYYY'))
    //   setSelectedMonth(periodStart.format('M'))

    //   // Update the period selection
    //   onPeriodSelect(bestOffset)

    //   // Reset the flag after a brief delay
    //   setTimeout(() => setIsUpdatingProgrammatically(false), 100)

    //   // Don't close the popover immediately - let user see the updated period list
    //   // onClose() - removed to keep popover open
    // }
    const handleResetToCurrent = () => {
      const now = new Date()
      const dayOfWeek = dayjs(now).day()
      const daysToSubtract = dayOfWeek === 0 ? 6 : dayOfWeek - 1

      const startOfCurrentWeek = dayjs(now)
        .subtract(daysToSubtract, 'day')
        .toDate()
      const endOfCurrentWeek = dayjs(startOfCurrentWeek).add(6, 'day').toDate()

      setSelectedRange({ from: startOfCurrentWeek, to: endOfCurrentWeek })
      setSelectedYear(moment().format('YYYY'))
      setSelectedMonth(moment().format('M'))

      // Reset local selection to current period (offset 0)
      setLocalSelectedOffset(0)
    }

    // Helper function to calculate effective payroll length based on active tab
    const getEffectivePayrollLength = React.useCallback(() => {
      let effectivePayrollLength = payrollLength
      if (activeWeekPeriodTab === 'biweekly') {
        effectivePayrollLength = 14 // Always use 14 days for biweekly
      } else if (activeWeekPeriodTab === 'week1' || activeWeekPeriodTab === 'week2') {
        effectivePayrollLength = payrollLength === 7 ? 14 : payrollLength // Use biweekly base for weekly tabs
      }
      return effectivePayrollLength
    }, [payrollLength, activeWeekPeriodTab])

    // Helper function to find period offset for a given month/year
    const findPeriodOffsetForMonthYear = React.useCallback((targetMonth: number, targetYear: number) => {
      const effectivePayrollLength = getEffectivePayrollLength()
      let foundOffset = 0

      // Check the first 28 days of the month to find a period that starts in this month
      for (let day = 1; day <= 28; day++) {
        const testDate = dayjs(`${targetYear}-${targetMonth.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`)
        const testOffset = getOffsetForDate(testDate, originalStartOfPeriod)

        // Check if this period actually starts in the target month
        // For weekly tabs, always use 14-day increments to find biweekly base
        const incrementLength = (activeWeekPeriodTab === 'week1' || activeWeekPeriodTab === 'week2') ? 14 : effectivePayrollLength
        const periodStart = originalStartOfPeriod.clone().add(testOffset * incrementLength, 'days')

        if (periodStart.month() === targetMonth - 1 && periodStart.year() === targetYear) {
          foundOffset = testOffset
          break
        }
      }

      return foundOffset
    }, [getEffectivePayrollLength, getOffsetForDate, originalStartOfPeriod, activeWeekPeriodTab])

    // Handler for month dropdown change
    const handleMonthChange = React.useCallback((option: OptionType) => {
      const newMonth = String(option.value)
      setSelectedMonth(newMonth)

      const targetMonth = parseInt(newMonth)
      const targetYear = parseInt(selectedYear)
      const foundOffset = findPeriodOffsetForMonthYear(targetMonth, targetYear)

      // Update local selection only, don't update context yet
      setLocalSelectedOffset(foundOffset)
    }, [selectedYear, findPeriodOffsetForMonthYear])

    // Handler for year dropdown change
    const handleYearChange = React.useCallback((option: OptionType) => {
      const newYear = String(option.value)
      setSelectedYear(newYear)

      const targetMonth = parseInt(selectedMonth)
      const targetYear = parseInt(newYear)
      const foundOffset = findPeriodOffsetForMonthYear(targetMonth, targetYear)

      // Update local selection only, don't update context yet
      setLocalSelectedOffset(foundOffset)
    }, [selectedMonth, findPeriodOffsetForMonthYear])

    // Calculate effective period based on local selection for date picker highlighting
    const localEffectivePeriod = React.useMemo(() => {
      // Calculate effective payroll length based on active tab
      let effectivePayrollLength = payrollLength
      if (activeWeekPeriodTab === 'biweekly') {
        effectivePayrollLength = 14 // Always use 14 days for biweekly
      } else if (activeWeekPeriodTab === 'week1' || activeWeekPeriodTab === 'week2') {
        effectivePayrollLength = payrollLength === 7 ? 14 : payrollLength // Use biweekly base for weekly tabs
      }
      
      // Use the ORIGINAL startOfPeriod for local calculations - no adjustments needed
      let normalizedStartOfPeriod = originalStartOfPeriod.clone()

      if (activeWeekPeriodTab === 'week1') {
        // Week 1: First 7 days of the biweekly period
        // Use biweekly increments (14 days) to find the base biweekly period
        let baseBiweeklyStart = normalizedStartOfPeriod
          .clone()
          .add(localSelectedOffset * 14, 'days')
        return {
          start: baseBiweeklyStart,
          length: 7
        }
      } else if (activeWeekPeriodTab === 'week2') {
        // Week 2: Second 7 days of the biweekly period
        // Use biweekly increments (14 days) to find the base biweekly period
        let baseBiweeklyStart = normalizedStartOfPeriod
          .clone()
          .add(localSelectedOffset * 14, 'days')
        return {
          start: baseBiweeklyStart.clone().add(7, 'days'),
          length: 7
        }
      } else {
        // Biweekly: Full period
        let basePeriodStart = normalizedStartOfPeriod
          .clone()
          .add(localSelectedOffset * effectivePayrollLength, 'days')
        return {
          start: basePeriodStart,
          length: effectivePayrollLength
        }
      }
    }, [originalStartOfPeriod, localSelectedOffset, payrollLength, activeWeekPeriodTab])

//    // update the month/year dropdowns when the local selection changes:
useEffect(() => {
  // Calculate effective payroll length based on active tab
  let effectivePayrollLength = payrollLength
  if (activeWeekPeriodTab === 'biweekly') {
    effectivePayrollLength = 14 // Always use 14 days for biweekly
  } else if (activeWeekPeriodTab === 'week1' || activeWeekPeriodTab === 'week2') {
    effectivePayrollLength = payrollLength === 7 ? 14 : payrollLength // Use biweekly base for weekly tabs
  }
  
  // Use ORIGINAL startOfPeriod for consistent calculations, no adjustments needed
  let normalizedStartOfPeriod = originalStartOfPeriod.clone()
  
  // For month/year dropdowns, use the locally selected period
  // This ensures dropdowns sync with local selection changes
  let basePeriodStart
  if (activeWeekPeriodTab === 'week1' || activeWeekPeriodTab === 'week2') {
    // For weekly tabs, always use 14-day increments to find the biweekly base
    basePeriodStart = normalizedStartOfPeriod
      .clone()
      .add(localSelectedOffset * 14, 'days')
  } else {
    // For biweekly tab, use the effective payroll length
    basePeriodStart = normalizedStartOfPeriod
      .clone()
      .add(localSelectedOffset * effectivePayrollLength, 'days')
  }



  setSelectedYear(basePeriodStart.format('YYYY'))
  setSelectedMonth(basePeriodStart.format('M'))

  // For the date picker highlighting, use the calculated effective period
  const newPeriodStart = localEffectivePeriod.start
  const newPeriodEnd = newPeriodStart.clone().add(localEffectivePeriod.length - 1, 'days')

  setSelectedRange({
    from: newPeriodStart.toDate(),
    to: newPeriodEnd.toDate()
  })
}, [originalStartOfPeriod, localSelectedOffset, payrollLength, localEffectivePeriod, activeWeekPeriodTab])
    return (
      <PopoverStyled
        ref={ref}
        {...props}
        {...rest}
      >
        <ContainerStyled>
          <CloseButtonStyled onClick={onClose}>
            <CloseIconStyled />
          </CloseButtonStyled>
          <MainStyled>
            <CalendarBlockStyled>
              <SelectBlockStyled>
                <CustomSelectStyled
                  options={monthsOptions}
                  value={monthsOptions.find(
                    ({ value }) => value === selectedMonth
                  )}
                  onChange={handleMonthChange}
                  components={{
                    IndicatorSeparator: () => null
                  }}
                />
                <CustomSelectStyled
                  options={yearOptions}
                  value={yearOptions.find(
                    ({ value }) => value === selectedYear
                  )}
                  onChange={handleYearChange}
                  components={{
                    IndicatorSeparator: () => null
                  }}
                />
              </SelectBlockStyled>

              <DayPickerStyled
                showOutsideDays
                hideNavigation
                animate
                mode='single'
                selected={selectedRange.from}
                // onSelect={handleDateSelect}
                locale={enUS}
                weekStartsOn={weekStartsOn as 0 | 1 | 2 | 3 | 4 | 5 | 6}
                month={new Date(parseInt(selectedYear), parseInt(selectedMonth) - 1)}
                key={`${selectedYear}-${selectedMonth}`}
                required
                formatters={{
                  formatWeekdayName: (day: Date) =>
                    dayjs(day).format('ddd').slice(0, -1)
                }}
                modifiers={{
                  weekRange: date => {
                    if (!selectedRange.from || !selectedRange.to) return false
                    const d = dayjs(date).startOf('day')
                    const from = dayjs(selectedRange.from).startOf('day')
                    const to = dayjs(selectedRange.to).startOf('day')
                    return (
                      (d.isSame(from) || d.isAfter(from)) &&
                      (d.isSame(to) || d.isBefore(to))
                    )
                  },
                  today: date => {
                    return dayjs(date).isSame(dayjs(), 'day')
                  }
                }}
                modifiersClassNames={{
                  weekRange: 'rdp-day_selected-week',
                  today: 'rdp-day_today'
                }}
              />
            </CalendarBlockStyled>
            <ListBlockStyled>
              <ListLabelStyled>
                {I18n.t('payroll.select_new_period')}
              </ListLabelStyled>
              <ListWrapStyled>
                <ListStyled>
                  {periodList.map((period) => (
                    <ListItemStyled
                      key={period.offset}
                      $isCurrentPeriod={period.isCurrentPeriod}
                      $isSelected={period.isSelected}
                      onClick={() => {
                        // Only update local selection, don't update context yet
                        setLocalSelectedOffset(period.offset)
                        // Don't close immediately - let user see the selection update
                      }}
                      $isLowerCase={isLocaleFr}
                    >
                      {period.label}
                      <CircleStyled>
                        {period.isSelected && <CheckIconStyled />}
                      </CircleStyled>
                    </ListItemStyled>
                  ))}
                </ListStyled>
              </ListWrapStyled>
            </ListBlockStyled>
          </MainStyled>

          <FooterStyled>
            <ResetButtonStyled onClick={handleResetToCurrent}>
              {I18n.t('payroll.reset_to_current')} ↩
            </ResetButtonStyled>
            <OpenButtonStyled
              disabled={false}
              onClick={() => {
                // Apply the local selection to context and close modal
                onPeriodSelect(localSelectedOffset)
                onClose()
              }}
            >
              {I18n.t('payroll.open_selected')}
            </OpenButtonStyled>
          </FooterStyled>
        </ContainerStyled>
      </PopoverStyled>
    )
  }
)

export default CalendarPopover
