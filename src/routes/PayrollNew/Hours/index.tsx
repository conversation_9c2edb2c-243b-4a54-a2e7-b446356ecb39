import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import Overlay from 'react-bootstrap/Overlay'
import { useSelector } from 'react-redux'
import { I18n } from 'react-redux-i18n'

import dayjs from 'dayjs'
import cloneDeep from 'lodash/cloneDeep'
import forEach from 'lodash/forEach'
import isEmpty from 'lodash/isEmpty'
import { RootState } from 'store/reducers'

import CalendarPopover from './components/CalendarPopover'
import HoursTable from './components/HoursTable'
import RollingNumber from './components/RollingNumber'
import { usePeriod } from 'contexts/PeriodContext'

import {
  TabListStyled,
  TabButtonStyled,
  WeekBlockWrapStyled,
  WeekBlockStyled,
  WeekTopBlockStyled,
  WeekTopBlockRowStyled,
  PeriodBlockStyled,
  CalendarButtonStyled,
  CalendarIconStyled,
  PeriodSliderStyled,
  PeriodArrowStyled,
  PeriodTextStyled,
  PeriodStatusStyled,
  WeekPeriodTabsStyled,
  ButtonStyled,
  ClockIconStyled,
  WeekDaysStyled,
  DayStyled,
  GreyOverlayStyled,
  ClaimedStatusStyled,
  ClaimedStatusBlockStyled,
  ClaimedTextStyled,
  SparkleIconStyled,
  ErrorContainer,
  LoadingContainer
} from '../styles/Hours.styles'

import { database, useAppContext } from '../../../index'
import PayrollConflictModal from '../../PayrollOld/modals/PayrollConflictModal'
import { matchClockInWithScheduledShift } from '../../PayrollOld/payrollUtils'

import {
  getCurrentUserInfo,
  logShiftChanges,
  logShiftDeletion
} from 'utils/payroll/activityLogger'
import {
  RoleFilterState,
  clearRoleFilterState,
  createInitialFilterState,
  extractRolesFromCompany,
  getSingleRoleInfo,
  isSingleBOHRole,
  loadRoleFilterState,
  saveRoleFilterState
} from 'utils/payroll/roleFilterUtils'
import {
  analyzeShiftConflicts
} from 'utils/payroll/shiftConflictUtils'

import {
  AttendanceSettings,
  AttendanceShift,
  AttendanceShifts,
  IAllConflictingAttendanceShifts
} from 'types/attendance'
import { Company, IPosition } from 'types/company'
import { IEmployee } from 'types/employee'

import calendarIcon from 'img/icons/calendarBlankIcon.svg'
import ArrowLeft from 'img/IconsHover/ArrowLeftGrey'
import ArrowRight from 'img/IconsHover/ArrowRightGrey'

// import noteIcon from 'img/icons/noteIcon.svg'

// Helper functions moved outside component to avoid re-creation on every render
const getDayIndexInStaticWeekDay = (staticWeekDays: any[], dayName: string) => {
  return staticWeekDays.findIndex(day => day.dayName === dayName)
}

const renderWeekDays = ({
  activeWeekPeriodTab,
  filteredPeriodData,
  staticWeekDays
}: {
  activeWeekPeriodTab: string
  filteredPeriodData: { startOfPeriod: dayjs.Dayjs; payrollLength: number }
  staticWeekDays: Array<{ dayName: string; displayText: string }>
}) => {
  const days = []
  const today = dayjs()
  
  if (activeWeekPeriodTab === 'biweekly') {
    // Show 14 days for biweekly, highlight today if in range
    const biweekStart = filteredPeriodData.startOfPeriod
    for (let i = 0; i < 14; i++) {
      const dayDate = biweekStart.clone().add(i, 'days')
      const isToday = dayDate.isSame(today, 'day')
      const dayIndex = i % staticWeekDays.length
      days.push(
        <DayStyled
          $isToday={isToday}
          key={i}
        >
          {staticWeekDays[dayIndex].displayText}
        </DayStyled>
      )
    }
  } else {
    // Show 7 days for week1/week2, highlight today if in range
    const weekStart = filteredPeriodData.startOfPeriod
    for (let i = 0; i < 7; i++) {
      const dayDate = weekStart.clone().add(i, 'days')
      const isToday = dayDate.isSame(today, 'day')
      days.push(
        <DayStyled
          $isToday={isToday}
          key={i}
        >
          {staticWeekDays[i].displayText}
        </DayStyled>
      )
    }
  }
  return days
}

interface HoursProps {
  employeesArray: IEmployee[]
  employeesByRole: {
    [roleId: string]: { role: IPosition; employees: IEmployee[] }
  }
  searchTerm: string
  setSearchTerm: (value: string) => void
  selectedPositionId: string
  setSelectedPositionId: (value: string) => void
  currentCompany: Company
  displayBy: string
  setDisplayBy: (value: string) => void
  displayByArray: Array<{ id: string; label: string; icon: React.ReactNode }>
  onSearchEmployee: (value: string) => void
  attendanceSettings: AttendanceSettings
  setAttendanceSettings: (settings: AttendanceSettings) => void
  hasPayrollIntegration: boolean
}

const Hours: React.FC<HoursProps> = ({
  employeesArray,
  employeesByRole,
  searchTerm,
  setSearchTerm: _setSearchTerm,
  selectedPositionId,
  setSelectedPositionId: _setSelectedPositionId,
  currentCompany,
  displayBy,
  setDisplayBy,
  displayByArray,
  onSearchEmployee,
  attendanceSettings,
  setAttendanceSettings: _setAttendanceSettings,
  hasPayrollIntegration
}) => {
  // Use period context for all period-related data
  const { period, actions } = usePeriod()
  const {
    basePeriodStart: startOfPeriod,
    payrollLength,
    currentPeriodOffset,
    currentPeriodStart,
    currentPeriodEnd,
    periodDisplay,
    startOfPeriodStr,
    endOfPeriodStr
  } = period

  // Attendance data state (will be loaded based on period context)
  const [attendanceData, setAttendanceData] = useState<AttendanceShifts>({})
  const [isDataLoaded, setIsDataLoaded] = useState(false)
  const [schedule, setSchedule] = useState<any>({}) // Schedule data for conflict detection
  const [isScheduleLoaded, setIsScheduleLoaded] = useState(false)

  // Error handling and loading states
  const [dataError, setDataError] = useState<string | null>(null)
  const [scheduleError, setScheduleError] = useState<string | null>(null)
  const [isRetrying, setIsRetrying] = useState(false)
  const [retryCount, setRetryCount] = useState(0)
  const [isOnline, setIsOnline] = useState(navigator.onLine)
  const maxRetries = 3

  // Network connectivity monitoring
  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true)
      // Auto-retry when connection is restored
      if ((dataError || scheduleError) && !isRetrying) {
        handleRetry()
      }
    }
    const handleOffline = () => setIsOnline(false)

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [dataError, scheduleError, isRetrying])

  // Auto-retry when user returns to the page (visibility API)
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden && (dataError || scheduleError) && !isRetrying && isOnline) {
        // User returned to the page and we have errors - auto retry
        setTimeout(() => {
          if ((dataError || scheduleError) && !isRetrying) {
            console.log('Auto-retrying due to page visibility change')
            handleRetry()
          }
        }, 1000) // Small delay to avoid immediate retry
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange)
  }, [dataError, scheduleError, isRetrying, isOnline])
  const { user, currentEmployee } = useAppContext()

  // Retry utility function with exponential backoff
  const retryWithBackoff = useCallback(async (
    operation: () => Promise<void>,
    attempt: number = 1
  ): Promise<void> => {
    try {
      await operation()
    } catch (error) {
      if (attempt < maxRetries) {
        const delay = Math.min(1000 * Math.pow(2, attempt - 1), 10000) // Max 10 seconds
        console.warn(`Attempt ${attempt} failed, retrying in ${delay}ms...`, error)

        setIsRetrying(true)
        setRetryCount(attempt)

        await new Promise(resolve => setTimeout(resolve, delay))
        return retryWithBackoff(operation, attempt + 1)
      } else {
        setIsRetrying(false)
        setRetryCount(0)
        throw error
      }
    }
  }, [maxRetries, setIsRetrying, setRetryCount])

  // Load attendance data and schedule data when period changes
  useEffect(() => {
    let attendanceRef: ReturnType<typeof database.ref> | null = null
    let scheduleRef: ReturnType<typeof database.ref> | null = null
    let isMounted = true

    const loadAttendanceData = (): Promise<void> => {
      return new Promise((resolve, reject) => {
        try {
          const attendanceQuery = database
            .ref('Attendance/' + currentCompany.key)
            .orderByKey()
            .startAt(startOfPeriodStr)
            .endAt(endOfPeriodStr)

          attendanceRef = database.ref('Attendance/' + currentCompany.key)

          const onValue = (snapshot: any) => {
            if (!isMounted) return

            try {
              const data = snapshot.val() || {}
              setAttendanceData(data)
              setIsDataLoaded(true)
              setDataError(null)
              resolve()
            } catch (error) {
              console.error('Error processing attendance data:', error)
              reject(new Error('Failed to process attendance data'))
            }
          }

          const onError = (error: any) => {
            console.error('Firebase attendance error:', error)
            reject(new Error(`Firebase attendance error: ${error.message || 'Unknown error'}`))
          }

          attendanceQuery.on('value', onValue, onError)
        } catch (error) {
          console.error('Error setting up attendance listener:', error)
          reject(new Error('Failed to setup attendance data listener'))
        }
      })
    }

    const loadScheduleData = (): Promise<void> => {
      return new Promise((resolve, reject) => {
        try {
          const scheduleQuery = database
            .ref('WeeklySchedule/' + currentCompany.key)
            .orderByKey()
            .startAt(startOfPeriodStr)
            .endAt(endOfPeriodStr)

          scheduleRef = database.ref('WeeklySchedule/' + currentCompany.key)

          const onValue = (snapshot: any) => {
            if (!isMounted) return

            try {
              const data = snapshot.val() || {}
              setSchedule(data)
              setIsScheduleLoaded(true)
              setScheduleError(null)
              resolve()
            } catch (error) {
              console.error('Error processing schedule data:', error)
              reject(new Error('Failed to process schedule data'))
            }
          }

          const onError = (error: any) => {
            console.error('Firebase schedule error:', error)
            reject(new Error(`Firebase schedule error: ${error.message || 'Unknown error'}`))
          }

          scheduleQuery.on('value', onValue, onError)
        } catch (error) {
          console.error('Error setting up schedule listener:', error)
          reject(new Error('Failed to setup schedule data listener'))
        }
      })
    }

    const loadData = async () => {
      if (!currentCompany.key) return

      try {
        // Reset error states
        setDataError(null)
        setScheduleError(null)
        setIsRetrying(false)
        setRetryCount(0)

        // Load both attendance and schedule data with retry logic
        await Promise.all([
          retryWithBackoff(loadAttendanceData).catch(error => {
            console.error('Failed to load attendance data after retries:', error)
            setDataError(error.message || 'Failed to load attendance data')
          }),
          retryWithBackoff(loadScheduleData).catch(error => {
            console.error('Failed to load schedule data after retries:', error)
            setScheduleError(error.message || 'Failed to load schedule data')
          })
        ])
      } catch (error) {
        console.error('Unexpected error during data loading:', error)
      } finally {
        setIsRetrying(false)
        setRetryCount(0)
      }
    }

    loadData()

    return () => {
      isMounted = false
      if (attendanceRef) {
        attendanceRef.off()
      }
      if (scheduleRef) {
        scheduleRef.off()
      }
      setIsScheduleLoaded(false)
      setIsDataLoaded(false)
      setDataError(null)
      setScheduleError(null)
      setIsRetrying(false)
      setRetryCount(0)
    }
  }, [currentCompany.key, startOfPeriodStr, endOfPeriodStr, retryWithBackoff])

  // Role filtering state and logic
  const departmentRoles = useMemo(
    () => extractRolesFromCompany(currentCompany.jobs || {}),
    [currentCompany.jobs]
  )

  const isSingleRole = useMemo(
    () => isSingleBOHRole(departmentRoles),
    [departmentRoles]
  )

  const singleRoleInfo = useMemo(
    () => getSingleRoleInfo(departmentRoles),
    [departmentRoles]
  )

  const [roleFilterState, setRoleFilterState] = useState<RoleFilterState>(
    () => {
      // Initialize with empty state first, will be properly set in useEffect
      return {
        selectedDepartments: [],
        selectedRoles: {},
        selectedSubcategories: {}
      }
    }
  )

  // Initialize filter state when department roles are available
  useEffect(() => {
    // Only proceed if we have department roles data
    if (
      departmentRoles.FOH.length === 0 &&
      departmentRoles.BOH.length === 0 &&
      departmentRoles.MNG.length === 0
    ) {
      return
    }

    // Clear any potentially corrupted saved state first
    clearRoleFilterState()

    const defaultState = createInitialFilterState(departmentRoles)
    setRoleFilterState(defaultState)
  }, [departmentRoles])

  // Handle view switching and saved state
  useEffect(() => {
    if (displayBy !== 'roles') {
      // Reset to default (all roles selected) when switching to "By Employee"
      clearRoleFilterState()
      if (
        departmentRoles.FOH.length > 0 ||
        departmentRoles.BOH.length > 0 ||
        departmentRoles.MNG.length > 0
      ) {
        const defaultState = createInitialFilterState(departmentRoles)
        setRoleFilterState(defaultState)
      }
    } else {
      // Load saved state when switching to "By Roles" - only run once when switching to roles view
      const saved = loadRoleFilterState()
      if (saved && saved.selectedDepartments.length > 0) {
        setRoleFilterState(saved)
      } else if (
        departmentRoles.FOH.length > 0 ||
        departmentRoles.BOH.length > 0 ||
        departmentRoles.MNG.length > 0
      ) {
        // Fallback to default state if no saved state
        const defaultState = createInitialFilterState(departmentRoles)
        setRoleFilterState(defaultState)
      }
    }
  }, [displayBy, departmentRoles]) // Removed roleFilterState.selectedDepartments.length from dependencies

  const handleRoleFilterChange = (newState: RoleFilterState) => {
    // Always update the local state immediately
    setRoleFilterState(newState)

    // Save to localStorage when in roles view
    if (displayBy === 'roles') {
      saveRoleFilterState(newState)
    }
  }

  const weekPeriodTabs = [
    { id: 'biweekly', label: I18n.t('payroll.biweekly') },
    { id: 'week1', label: I18n.t('payroll.week') + ' 1' },
    { id: 'week2', label: I18n.t('payroll.week') + ' 2' }
  ]
  const [activeWeekPeriodTab, setActiveWeekPeriodTab] = useState('biweekly')
  // Debug: log tab changes
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('[DEBUG] activeWeekPeriodTab:', activeWeekPeriodTab)
    }
  }, [activeWeekPeriodTab])

  const isLocaleFr =
    useSelector((state: RootState) => state.i18n.locale) === 'fr'

  // Use period display from context, adjusted for active week tab
  const currentDisplayPeriod = React.useMemo(() => {
    // Calculate effective period based on active week tab
    let effectiveStart = currentPeriodStart
    let effectiveEnd = currentPeriodEnd

    if (activeWeekPeriodTab === 'biweekly') {
      // Always use 14 days for biweekly
      effectiveStart = currentPeriodStart
      effectiveEnd = currentPeriodStart.clone().add(13, 'days')
    } else if (activeWeekPeriodTab === 'week1') {
      // Week 1: First 7 days of the bi-weekly period
      effectiveStart = currentPeriodStart
      effectiveEnd = currentPeriodStart.clone().add(6, 'days')
    } else if (activeWeekPeriodTab === 'week2') {
      // Week 2: Second 7 days of the bi-weekly period
      effectiveStart = currentPeriodStart.clone().add(7, 'days')
      effectiveEnd = currentPeriodStart.clone().add(13, 'days')
    }

    return {
      start: effectiveStart,
      end: effectiveEnd,
      startStr: effectiveStart.format(isLocaleFr ? 'DD MMM' : 'MMM DD'),
      endStr: effectiveEnd.format(isLocaleFr ? 'DD MMM' : 'MMM DD'),
      isCurrentPeriod: periodDisplay.isCurrentPeriod,
      isPastPeriod: periodDisplay.isPastPeriod,
      isFuturePeriod: periodDisplay.isFuturePeriod
    }
  }, [currentPeriodStart, currentPeriodEnd, activeWeekPeriodTab, isLocaleFr, periodDisplay])

  const isPastPeriod = currentDisplayPeriod.isPastPeriod
  const isFuturePeriod = currentDisplayPeriod.isFuturePeriod

  // Handle period selection from CalendarPopover
  const handlePeriodSelect = (offset: number) => {
    // When activeWeekPeriodTab is biweekly but payrollLength is 7 (weekly),
    // we need to adjust the offset to account for the difference
    let adjustedOffset = offset

    if (activeWeekPeriodTab === 'biweekly' && payrollLength === 7) {
      // Convert biweekly offset to weekly offset
      // Each biweekly period = 2 weekly periods
      adjustedOffset = offset * 2
    }

    // Update the user period offset in context
    actions.setUserPeriodOffset(adjustedOffset)
  }

  // Calculate filtered period data based on active week tab
  const filteredPeriodData = useMemo(() => {
    // Calculate the effective period length and offset based on active tab
    let effectivePayrollLength = payrollLength
    let effectiveOffset = currentPeriodOffset

    if (activeWeekPeriodTab === 'biweekly' && payrollLength === 7) {
      // When viewing biweekly but payroll is weekly, convert offset
      effectivePayrollLength = 14
      effectiveOffset = Math.floor(currentPeriodOffset / 2)
    } else if ((activeWeekPeriodTab === 'week1' || activeWeekPeriodTab === 'week2') && payrollLength === 14) {
      // When viewing weekly but payroll is biweekly, convert offset
      effectivePayrollLength = 14
      effectiveOffset = currentPeriodOffset
    }

    // Use fixed startOfPeriod + offset approach
    const basePeriodStart = startOfPeriod
      .clone()
      .add(effectiveOffset * effectivePayrollLength, 'days')

    let filteredStartOfPeriod = basePeriodStart
    let filteredPayrollLength = effectivePayrollLength

    if (activeWeekPeriodTab === 'biweekly') {
      // Always use 14 days for biweekly
      filteredPayrollLength = 14
    } else if (activeWeekPeriodTab === 'week1') {
      // First week of the biweekly period
      filteredPayrollLength = 7
    } else if (activeWeekPeriodTab === 'week2') {
      // Second week of the biweekly period
      filteredStartOfPeriod = basePeriodStart.clone().add(7, 'days')
      filteredPayrollLength = 7
    }

    return {
      startOfPeriod: filteredStartOfPeriod,
      payrollLength: filteredPayrollLength
    }
  }, [startOfPeriod, payrollLength, currentPeriodOffset, activeWeekPeriodTab])


  const staticWeekDays = useMemo(() => {
    // Monday = 1, Sunday = 7
    const weekKeys = [
      'jobs.Monday',
      'jobs.Tuesday',
      'jobs.Wednesday',
      'jobs.Thursday',
      'jobs.Friday',
      'jobs.Saturday',
      'jobs.Sunday'
    ]
    const weekStartingDay = currentCompany.payrollStartingDay || 'Monday'
    const startingIndex = weekKeys.indexOf('jobs.' + weekStartingDay)
    weekKeys.push(...weekKeys.splice(0, startingIndex))
    return weekKeys.map(key => {
      const dayName = I18n.t(key)
      return {
        dayName,
        displayText: dayName
      }
    })
  }, [currentCompany.payrollStartingDay])
  
  const currentDayIndex = useMemo(() => {
    const today = dayjs().day() // 0 = Sunday, 1 = Monday, ..., 6 = Saturday
    const weekDayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
    return getDayIndexInStaticWeekDay(staticWeekDays, weekDayNames[today])
  }, [staticWeekDays])
  // Filter attendance data based on the selected week tab
  const filteredAttendanceData = useMemo(() => {
    if (activeWeekPeriodTab === 'biweekly') {
      console.log('[DEBUG] Returning all attendanceData for biweekly:', attendanceData)
      return attendanceData // Return all data for biweekly view
    }

    const filtered: AttendanceShifts = {}
    const { startOfPeriod: periodStart, payrollLength: periodLength } =
      filteredPeriodData

    // Generate date keys for the filtered period
    for (let i = 0; i < periodLength; i++) {
      const dateKey = periodStart.clone().add(i, 'days').format('YYYY-MM-DD')
      if (attendanceData[dateKey]) {
        filtered[dateKey] = attendanceData[dateKey]
      }
    }

    console.log('[DEBUG] Filtered attendanceData for', activeWeekPeriodTab, filtered)
    return filtered
  }, [attendanceData, activeWeekPeriodTab, filteredPeriodData])

  // Helper function to calculate total salary for a given period
  const calculatePeriodSalary = React.useCallback(
    (periodStart: dayjs.Dayjs, periodEnd: dayjs.Dayjs) => {
      let totalSalary = 0

      // Iterate through each day in the period
      let currentDay = periodStart.clone()
      while (currentDay.isSameOrBefore(periodEnd)) {
        const dateKey = currentDay.format('YYYY-MM-DD')
        const dayShifts = attendanceData[dateKey]

        if (dayShifts) {
          // Calculate salary for all employees on this day
          for (const employee of employeesArray) {
            const employeeShifts = dayShifts[employee.uid]
            if (employeeShifts) {
              for (const shift of Object.values(employeeShifts)) {
                totalSalary +=
                  (shift as AttendanceShift & { salary?: number }).salary || 0
              }
            }
          }
        }

        currentDay = currentDay.add(1, 'day')
      }

      return totalSalary
    },
    [attendanceData, employeesArray]
  )

  // Calculate salary savings: current period vs previous period
  const salarySavings = useMemo(() => {
    // For current or future periods, always use current vs previous comparison
    const isCurrentOrFuture = currentPeriodOffset >= 0

    let displayedPeriodSalary = 0
    let comparisonPeriodSalary = 0

    if (isCurrentOrFuture) {
      // For current/future periods: compare current period with previous period
      // Calculate current period (offset 0) salary
      const actualCurrentStart = startOfPeriod.clone()
      const actualCurrentEnd = actualCurrentStart
        .clone()
        .add(payrollLength - 1, 'days')

      // Calculate previous period (offset -1) salary
      const actualPreviousStart = startOfPeriod
        .clone()
        .add(-1 * payrollLength, 'days')
      const actualPreviousEnd = actualPreviousStart
        .clone()
        .add(payrollLength - 1, 'days')

      displayedPeriodSalary = calculatePeriodSalary(
        actualCurrentStart,
        actualCurrentEnd
      )
      comparisonPeriodSalary = calculatePeriodSalary(
        actualPreviousStart,
        actualPreviousEnd
      )
    } else {
      // For past periods: compare selected period with its previous period
      const currentPeriodStart = startOfPeriod
        .clone()
        .add(currentPeriodOffset * payrollLength, 'days')
      const currentPeriodEnd = currentPeriodStart
        .clone()
        .add(payrollLength - 1, 'days')

      const previousPeriodStart = startOfPeriod
        .clone()
        .add((currentPeriodOffset - 1) * payrollLength, 'days')
      const previousPeriodEnd = previousPeriodStart
        .clone()
        .add(payrollLength - 1, 'days')

      displayedPeriodSalary = calculatePeriodSalary(
        currentPeriodStart,
        currentPeriodEnd
      )
      comparisonPeriodSalary = calculatePeriodSalary(
        previousPeriodStart,
        previousPeriodEnd
      )
    }

    const savings = comparisonPeriodSalary - displayedPeriodSalary

    return Math.max(0, savings) // Only show positive savings
  }, [startOfPeriod, currentPeriodOffset, payrollLength, calculatePeriodSalary])

  // Calculate conflicting shifts for the current period
  const allConflictingShifts = useMemo(() => {
    const conflicts: IAllConflictingAttendanceShifts = {}
    const currentPeriodStart = startOfPeriod
      .clone()
      .add(currentPeriodOffset * payrollLength, 'days')
    const currentPeriodEnd = currentPeriodStart
      .clone()
      .add(payrollLength - 1, 'days')

    // Iterate through each day in the current period
    let currentDay = currentPeriodStart.clone()

    while (currentDay.isSameOrBefore(currentPeriodEnd)) {
      const dateKey = currentDay.format('YYYY-MM-DD')
      const dayShifts = attendanceData[dateKey]

      if (dayShifts) {
        const currentDayCopy = currentDay.clone()
        forEach(dayShifts, (employeeShifts, employeeId) => {
          forEach(employeeShifts, (shift, shiftKey) => {
            if (!shift.isConfirmed) {
              // Apply rounding to shift times (matching PayrollOld logic)
              const roundingTime = attendanceSettings?.roundingTime || 15
              const shiftStartRounded = shift.start ? Math.round(shift.start / roundingTime) * roundingTime : 0
              const shiftEndRounded = shift.end ? Math.round(shift.end / roundingTime) * roundingTime : null

              // Find matching scheduled shift using actual schedule data
              const scheduledPositions = schedule?.[dateKey]?.[employeeId] || {}
              let scheduledShift = null

              if (!isEmpty(scheduledPositions) && shift.start !== undefined) {
                const dayShifts: any[] = []

                forEach(scheduledPositions, (subpositions, positionId) => {
                  forEach(subpositions, (subpositionShifts, subcategoryId) => {
                    forEach(subpositionShifts, (scheduledShiftData, shiftKey) => {
                      dayShifts.push({
                        ...scheduledShiftData,
                        positionId,
                        subcategoryId,
                        shiftKey
                      })
                    })
                  })
                })

                const matchedShift = matchClockInWithScheduledShift({
                  dayShifts,
                  shiftStartRounded,
                  defaultDuration: currentCompany?.defaultDuration || 480,
                  shift: {
                    start: shiftStartRounded,
                    end: shiftEndRounded
                  }
                })

                if (matchedShift) {
                  scheduledShift = matchedShift
                }
              }

              // Analyze shift conflicts
              const conflictAnalysis = analyzeShiftConflicts(
                shift,
                shiftStartRounded,
                shiftEndRounded,
                scheduledShift,
                currentDayCopy.isSame(dayjs(), 'day'),
                false // TODO: Calculate actual working status
              )

              if (conflictAnalysis.isConflictingShift) {
                if (!conflicts[dateKey]) {
                  conflicts[dateKey] = {}
                }
                if (!conflicts[dateKey][employeeId]) {
                  conflicts[dateKey][employeeId] = {}
                }

                conflicts[dateKey][employeeId][shiftKey] = {
                  ...shift,
                  neverClockedOut: conflictAnalysis.notClockedOut,
                  scheduledShift,
                  missingPosition: !shift.positionId,
                  isClockInDifferent: conflictAnalysis.isClockInDifferent,
                  isClockOutDiffrent: conflictAnalysis.isClockOutDifferent,
                  shiftStartRounded,
                  shiftEndRounded: shiftEndRounded || 0
                }
              }
            }
          })
        })
      }

      currentDay = currentDay.add(1, 'day')
    }

    return conflicts
  }, [
    attendanceData,
    schedule,
    startOfPeriod,
    currentPeriodOffset,
    payrollLength,
    currentCompany,
    attendanceSettings?.roundingTime
  ])

  // Count total number of conflicts
  const numberOfConflicts = useMemo(() => {
    let count = 0
    forEach(allConflictingShifts, dayShifts => {
      forEach(dayShifts, employeeShifts => {
        count += Object.keys(employeeShifts).length
      })
    })
    return count
  }, [allConflictingShifts])

  const [showCalendar, setShowCalendar] = useState(false)
  const calendarRef = useRef<HTMLDivElement>(null)
  const [showConflictModal, setShowConflictModal] = useState(false)

  // Save shift function (ported from PayrollOld)
  const onSave = async (
    newShift: { [key: string]: AttendanceShift },
    employeeId: string,
    date: string
  ) => {
    const copy = cloneDeep(attendanceData)

    if (!copy[date]) {
      copy[date] = {}
    }

    if (!copy[date][employeeId]) {
      copy[date][employeeId] = {}
    }

    // Get old shift data for activity logging
    const oldShifts = copy[date][employeeId] || {}

    // Update the shifts
    copy[date][employeeId] = {
      ...copy[date][employeeId],
      ...newShift
    }

    // Log activity for each changed shift
    const userInfo = getCurrentUserInfo(user, currentEmployee)
    for (const [shiftKey, shiftData] of Object.entries(newShift)) {
      const oldShift = oldShifts[shiftKey] || null
      await logShiftChanges(
        currentCompany.key,
        date,
        employeeId,
        oldShift,
        shiftData,
        shiftKey,
        userInfo.id,
        userInfo.name,
        userInfo.avatar
      )
    }

    setAttendanceData(copy)

    // Persist changes to Firebase database
    try {
      const updates: { [path: string]: any } = {}
      for (const [shiftKey, shiftData] of Object.entries(newShift)) {
        updates[
          `Attendance/${currentCompany.key}/${date}/${employeeId}/${shiftKey}`
        ] = shiftData
      }
      await database.ref().update(updates)
    } catch (error) {
      console.error('Failed to save shift to database:', error)
    }
  }

  // Delete shift function (ported from PayrollOld)
  const onDeleteShift = async (
    shiftKey: string,
    employeeId: string,
    date: string
  ) => {
    const copy = cloneDeep(attendanceData)

    if (copy[date] && copy[date][employeeId]) {
      // Get the shift data before deletion for activity logging
      const deletedShift = copy[date][employeeId][shiftKey]

      if (deletedShift) {
        // Log the deletion activity
        const userInfo = getCurrentUserInfo(user, currentEmployee)
        await logShiftDeletion(
          currentCompany.key,
          date,
          employeeId,
          deletedShift,
          shiftKey,
          userInfo.id,
          userInfo.name,
          userInfo.avatar
        )
      }

      delete copy[date][employeeId][shiftKey]

      // Persist deletion to Firebase database
      try {
        await database
          .ref(
            `Attendance/${currentCompany.key}/${date}/${employeeId}/${shiftKey}`
          )
          .remove()
      } catch (error) {
        console.error('Failed to delete shift from database:', error)
      }
    }

    setAttendanceData(copy)
  }

  const claimedAmount = 8114.76

  const [animationKey, setAnimationKey] = useState(0)
  const handleMouseEnter = () => {
    setAnimationKey(prev => prev + 1) // triggers rerender
  }

  // Error retry handler
  const handleRetry = () => {
    setDataError(null)
    setScheduleError(null)
    setRetryCount(0)
    // Trigger data reload by updating a dependency
    window.location.reload()
  }

  // Graceful degradation - determine if we should show degraded view
  const shouldShowDegradedView = (dataError || scheduleError) && retryCount >= maxRetries
  const hasPartialData = isDataLoaded || isScheduleLoaded

  return (
    <>
      {/* Error Display */}
      {(dataError || scheduleError) && (
        <ErrorContainer>
          <div className="error-content">
            <div className="error-icon">⚠️</div>
            <div className="error-details">
              <div className="error-title">
                {shouldShowDegradedView ? 'Running in Limited Mode' : 'Data Loading Error'}
              </div>
              {!isOnline && (
                <div className="error-message">
                  <strong>No internet connection</strong> - Please check your network
                </div>
              )}
              {dataError && (
                <div className="error-message">
                  Attendance: {dataError}
                  {!isOnline && ' (offline)'}
                </div>
              )}
              {scheduleError && (
                <div className="error-message">
                  Schedule: {scheduleError}
                  {!isOnline && ' (offline)'}
                </div>
              )}
              {isRetrying && (
                <div className="retry-info">
                  Retrying... (Attempt {retryCount}/{maxRetries})
                </div>
              )}
              {shouldShowDegradedView && hasPartialData && (
                <div className="degraded-info">
                  Some features may be limited. Showing available data.
                </div>
              )}
              {shouldShowDegradedView && !hasPartialData && (
                <div className="degraded-info">
                  Unable to load data. Please check your connection and try again.
                </div>
              )}
            </div>
            <button className="retry-button" onClick={handleRetry} disabled={isRetrying}>
              {isRetrying ? 'Retrying...' : shouldShowDegradedView ? 'Try Again' : 'Retry'}
            </button>
          </div>
        </ErrorContainer>
      )}

      {/* Loading Indicator */}
      {(!isDataLoaded || !isScheduleLoaded) && !dataError && !scheduleError && (
        <LoadingContainer>
          <div className="loading-content">
            <div className="loading-spinner"></div>
            <div className="loading-text">
              Loading payroll data...
              {isRetrying && ` (Retry ${retryCount}/${maxRetries})`}
            </div>
          </div>
        </LoadingContainer>
      )}

      <WeekBlockWrapStyled>
        <WeekBlockStyled>
          <WeekTopBlockStyled>
            <WeekTopBlockRowStyled>
              <PeriodBlockStyled ref={calendarRef}>
                <CalendarButtonStyled
                  onClick={() => setShowCalendar(!showCalendar)}
                >
                  <CalendarIconStyled
                    src={calendarIcon}
                    alt=''
                  />
                </CalendarButtonStyled>

                <PeriodSliderStyled>
                  
                  <PeriodArrowStyled
                    onClick={() => handlePeriodSelect(currentPeriodOffset - 1)}
                  >
                    <ArrowLeft />
                  </PeriodArrowStyled>
                  <PeriodTextStyled
                    onClick={() => setShowCalendar(!showCalendar)}
                    $isLowerCase={isLocaleFr}
                  >
                    {currentDisplayPeriod.startStr} -{' '}
                    {currentDisplayPeriod.endStr}
                  </PeriodTextStyled>
                    <PeriodArrowStyled
                    onClick={() => handlePeriodSelect(currentPeriodOffset + 1)}
                  >
                    <ArrowRight />
                  </PeriodArrowStyled>
                </PeriodSliderStyled>
                <PeriodStatusStyled
                  $isCurrentPeriod={!isPastPeriod && !isFuturePeriod}
                  onClick={() => setShowCalendar(!showCalendar)}
                >
                  {isPastPeriod
                    ? I18n.t('payroll.past_period')
                    : isFuturePeriod
                      ? I18n.t('payroll.upcoming')
                      : I18n.t('payroll.current_period')}
                </PeriodStatusStyled>
              </PeriodBlockStyled>

              {showCalendar && (
                <GreyOverlayStyled onClick={() => setShowCalendar(false)} />
              )}
              <Overlay
                rootClose
                show={showCalendar}
                placement='bottom'
                onHide={() => setShowCalendar(false)}
                target={() => calendarRef.current}
              >
                <CalendarPopover
                  onClose={() => setShowCalendar(false)}
                  startOfPeriod={startOfPeriod}
                  payrollLength={payrollLength}
                  currentPeriodOffset={currentPeriodOffset}
                  onPeriodSelect={handlePeriodSelect}
                  attendanceSettings={attendanceSettings}
                  payrollStartingDay={currentCompany.payrollStartingDay}
                  activeWeekPeriodTab={activeWeekPeriodTab}
                />
              </Overlay>

              <WeekPeriodTabsStyled role='tablist'>
                {weekPeriodTabs.map(tab => (
                  <TabButtonStyled
                    key={tab.id}
                    role='tab'
                    aria-selected={activeWeekPeriodTab === tab.id}
                    onClick={() => setActiveWeekPeriodTab(tab.id)}
                    $isActive={activeWeekPeriodTab === tab.id}
                  >
                    {tab.label}
                  </TabButtonStyled>
                ))}
              </WeekPeriodTabsStyled>
            </WeekTopBlockRowStyled>
            <WeekTopBlockRowStyled>
              {/* 
             Will be added at stage 2
              <ButtonStyled>
                <NoteIconStyled
                  src={noteIcon}
                  alt=''
                />
                {I18n.t('payroll.notes')}
                <span>0</span>
              </ButtonStyled>
               */}
              <ClaimedStatusStyled onMouseEnter={handleMouseEnter}>
                <SparkleIconStyled key={animationKey} />
                <ClaimedStatusBlockStyled>
                  <ClaimedTextStyled>
                    {I18n.t('payroll.you_saved')}
                  </ClaimedTextStyled>
                  <RollingNumber value={claimedAmount} />
                </ClaimedStatusBlockStyled>
              </ClaimedStatusStyled>

              <ButtonStyled
                $isOrange={numberOfConflicts > 0}
                onClick={() => setShowConflictModal(true)}
              >
                <ClockIconStyled />
                {I18n.t('payroll.conflicting_shifts')}
                <span>{numberOfConflicts}</span>
              </ButtonStyled>
            </WeekTopBlockRowStyled>
          </WeekTopBlockStyled>
          <WeekDaysStyled>
            {renderWeekDays({
              activeWeekPeriodTab,
              filteredPeriodData,
              staticWeekDays
            })}
          </WeekDaysStyled>
        </WeekBlockStyled>
      </WeekBlockWrapStyled>

      {/* Debug block removed after fix */}
      <HoursTable
        employeesArray={employeesArray.map((employee, index) => ({
          id: index + 1,
          name: employee.name || '',
          surname: employee.surname || '',
          avatar: employee.avatar || '',
          userId: employee.userId || '',
          uid: employee.uid || '',
          positions: employee.positions || [],
          payrollId: employee.payrollId,
          customId: employee.customId
        }))}
        employeesByRole={employeesByRole}
        searchEmployee={searchTerm}
        onSearchEmployee={onSearchEmployee}
        displayBy={displayBy}
        setDisplayBy={setDisplayBy}
        displayByArray={displayByArray}
        attendanceData={filteredAttendanceData}
        currentCompany={currentCompany}
        isDataLoaded={isDataLoaded}
        selectedPositionId={selectedPositionId}
        setSelectedPositionId={_setSelectedPositionId}
        startOfPeriod={filteredPeriodData.startOfPeriod}
        currentPeriodOffset={currentPeriodOffset}
        payrollLength={filteredPeriodData.payrollLength}
        onSave={onSave}
        onDeleteShift={onDeleteShift}
        departmentRoles={departmentRoles}
        roleFilterState={roleFilterState}
        onRoleFilterChange={handleRoleFilterChange}
        isSingleRole={isSingleRole}
        singleRoleInfo={singleRoleInfo}
        hasPayrollIntegration={hasPayrollIntegration}
      />

      <PayrollConflictModal
        key={showConflictModal ? 'open' : 'closed'} // ✅ Force re-mount on open/close
        showModal={showConflictModal}
        onClose={() => setShowConflictModal(false)}
        conflicts={cloneDeep(allConflictingShifts)}
        roundingTime={15}
        companyId={currentCompany?.key || ''}
        jobs={currentCompany?.jobs || {}}
      />
      {/* TODO add new modal */}
      {/* <ConflictShiftModal
        show={showConflictModal}
        onHide={() => setShowConflictModal(false)}
      /> */}
    </>
  )
}

export default Hours
