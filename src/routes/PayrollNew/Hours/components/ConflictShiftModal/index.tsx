import React, { useRef, useState } from 'react'
import { I18n } from 'react-redux-i18n'

import ShiftCard from './ShiftCard'

import {
  ModalStyled,
  HeaderStyled,
  TitleStyled,
  SubtitleStyled,
  CloseButtonStyled,
  CloseIconStyled,
  BodyStyled,
  ListStyled,
  ListItemStyled,
  BlockStyled,
  PeriodWrapStyled,
  PeriodTabsStyled,
  PeriodItemStyled,
  ScrollContainerStyled,
  TypeSectionStyled,
  CardEmptyStyled
} from '../../../styles/ConflictShiftModal.styles'

import checkIcon from 'img/icons/checkBlueIcon.svg'
import flagIcon from 'img/icons/flagIcon.svg'

// Helper function to handle type click and scrolling - moved outside component for performance
const handleTypeClickAction = (
  typeId: string,
  typeRefs: React.MutableRefObject<{ [key: string]: HTMLDivElement | null }>,
  scrollContainerRef: React.RefObject<HTMLDivElement>,
  setActiveType: (typeId: string) => void
) => {
  setActiveType(typeId)
  const targetRef = typeRefs.current[typeId]
  if (targetRef && scrollContainerRef.current) {
    targetRef.scrollIntoView({ behavior: 'smooth', block: 'start' })
  }
}

// Helper function to create type ref callback - moved outside component for performance
const createTypeRefSetter = (
  typeId: string,
  typeRefs: React.MutableRefObject<{ [key: string]: HTMLDivElement | null }>
) => (el: HTMLDivElement | null) => {
  typeRefs.current[typeId] = el
}

const ConflictShiftModal = ({
  show,
  onHide
}: {
  show: boolean
  onHide: () => void
}) => {
  const [activeType, setActiveType] = useState('clocked-in-early')
  const scrollContainerRef = useRef<HTMLDivElement>(null)
  const typeRefs = useRef<{ [key: string]: HTMLDivElement | null }>({})

  const typeList = [
    {
      id: 'clocked-in-early',
      title: I18n.t('payroll.clocked_in_early'),
      count: 1
    },
    {
      id: 'missing-end',
      title: I18n.t('payroll.missing_end'),
      count: 1
    },
    {
      id: 'clocked-out-late',
      title: I18n.t('payroll.clocked_out_late'),
      count: 0
    },
    {
      id: 'unplanned-shift',
      title: I18n.t('payroll.unplanned_shift'),
      count: 0
    },
    {
      id: 'shift-too-short',
      title: I18n.t('payroll.shift_too_short'),
      count: 0
    },
    {
      id: 'shift-under-3-hours',
      title: I18n.t('payroll.shift_under_3_hours'),
      count: 0
    }
  ]

  const [activePeriod, setActivePeriod] = useState('entire-period')
  const periodList = [
    {
      id: 'entire-period',
      title: I18n.t('payroll.entire_period'),
      count: 8
    },
    {
      id: 'week-1',
      title: I18n.t('payroll.week') + ' 1',
      count: 3
    },
    {
      id: 'week-2',
      title: I18n.t('payroll.week') + ' 2',
      count: 0
    },
    {
      id: 'today',
      title: I18n.t('payroll.today'),
      count: 0
    }
  ]

  const conflictItems: string[] = ['1', '2', '3', '4']

  const handleTypeClick = (typeId: string) => {
    handleTypeClickAction(typeId, typeRefs, scrollContainerRef, setActiveType)
  }

  const setTypeRef = (typeId: string) => createTypeRefSetter(typeId, typeRefs)

  return (
    <ModalStyled
      show={show}
      onHide={onHide}
    >
      <HeaderStyled>
        <TitleStyled>
          <img
            src={flagIcon}
            alt=''
          />
          {I18n.t('payroll.conflicting_shifts')}
        </TitleStyled>
        <SubtitleStyled>
          {I18n.t(
            'payroll.please_check_the_time_cards_below_to_ensure_proper_payroll_management'
          )}
        </SubtitleStyled>
        <CloseButtonStyled onClick={onHide}>
          <CloseIconStyled />
        </CloseButtonStyled>
      </HeaderStyled>
      <BodyStyled>
        <ListStyled>
          {typeList.map(item => (
            <ListItemStyled
              $isActive={item.id === activeType}
              onClick={() => handleTypeClick(item.id)}
              key={item.id}
            >
              {item.title}
              {item.count > 0 && <span>{item.count}</span>}
            </ListItemStyled>
          ))}
        </ListStyled>
        <BlockStyled>
          <PeriodWrapStyled>
            <PeriodTabsStyled>
              {/* Sidebar is ordered by priority:
Clocked in Early
Missing End
Unplanned shift
Shift under 3hrs
Shift too short
Clocked out late
Unless the category has no conflicts, in which case it will be moved to the bottom of the list
*/}
              {periodList.map(item => (
                <PeriodItemStyled
                  $isActive={item.id === activePeriod}
                  onClick={() => setActivePeriod(item.id)}
                  key={item.id}
                >
                  {item.title} {item.count > 0 && <span>{item.count}</span>}
                </PeriodItemStyled>
              ))}
            </PeriodTabsStyled>
          </PeriodWrapStyled>
          <ScrollContainerStyled ref={scrollContainerRef}>
            {conflictItems.length > 0 ? (
              typeList.map(type => (
                <TypeSectionStyled
                  key={type.id}
                  ref={setTypeRef(type.id)}
                >
                  <ShiftCard
                    activeType={type.id}
                    conflictItems={conflictItems}
                  />
                </TypeSectionStyled>
              ))
            ) : (
              <CardEmptyStyled>
                <img
                  src={checkIcon}
                  alt=''
                />
                {I18n.t('payroll.you_are_all_caught_up')}
              </CardEmptyStyled>
            )}
          </ScrollContainerStyled>
        </BlockStyled>
      </BodyStyled>
    </ModalStyled>
  )
}

export default ConflictShiftModal
